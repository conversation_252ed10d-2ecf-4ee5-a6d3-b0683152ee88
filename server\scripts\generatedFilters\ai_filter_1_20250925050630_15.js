/**
 * AI自动生成的筛选脚本 (勿直接在生产执行前未审核情况下使用)
 * 生成时间: 2025-09-25T05:06:30.885Z
 * 逻辑索引: 1
 * 逻辑原始描述: 筛选连续三年净资产收益率大于15%、连续五年净利润增长率大于5%、当前市盈率小于20且股息率大于3%的股票
 *
 * 使用说明:
 *  1. 可通过 require 动态载入后在沙箱中执行(当前执行服务支持字符串形式)。
 *  2. 如需持久化版本控制，请将本文件纳入 git 并代码审查。
 *  3. 修改本文件后请注意不要引入非白名单模块。
 */

const executeLogic = async () => {
  try {
    // 获取当前年份和需要查询的年份范围
    const currentYear = new Date().getFullYear();
    const latestAvailableYear = 2024; // 财务数据滞后约束
    
    // 连续三年净资产收益率大于15%的年份范围
    const roeYears = [latestAvailableYear - 2, latestAvailableYear - 1, latestAvailableYear];
    // 连续五年净利润增长率大于5%的年份范围
    const profitGrowthYears = [latestAvailableYear - 4, latestAvailableYear - 3, latestAvailableYear - 2, latestAvailableYear - 1, latestAvailableYear];
    
    // 第一步：查询满足连续三年净资产收益率条件的股票
    const roeStocks = await FinancialData.aggregate([
      {
        $match: {
          fiscalYear: { $in: roeYears },
          reportType: "按年度",
          "data.keyMetrics.returnOnEquity": { $gt: 15, $ne: null }
        }
      },
      {
        $group: {
          _id: "$stockCode",
          roeYears: { $push: "$fiscalYear" },
          roeValues: { $push: "$data.keyMetrics.returnOnEquity" }
        }
      },
      {
        $match: {
          roeYears: { $size: 3 },
          $expr: {
            $and: [
              { $in: [roeYears[0], "$roeYears"] },
              { $in: [roeYears[1], "$roeYears"] },
              { $in: [roeYears[2], "$roeYears"] }
            ]
          }
        }
      },
      {
        $project: {
          stockCode: "$_id",
          _id: 0
        }
      }
    ]);
    
    if (roeStocks.length === 0) {
      return [];
    }
    
    const roeStockCodes = roeStocks.map(stock => stock.stockCode);
    
    // 第二步：查询满足连续五年净利润增长率条件的股票
    const profitGrowthStocks = await FinancialData.aggregate([
      {
        $match: {
          stockCode: { $in: roeStockCodes },
          fiscalYear: { $in: profitGrowthYears },
          reportType: "按年度",
          "data.keyMetrics.netProfitGrowthRate": { $gt: 5, $ne: null }
        }
      },
      {
        $group: {
          _id: "$stockCode",
          growthYears: { $push: "$fiscalYear" },
          growthValues: { $push: "$data.keyMetrics.netProfitGrowthRate" }
        }
      },
      {
        $match: {
          growthYears: { $size: 5 },
          $expr: {
            $and: [
              { $in: [profitGrowthYears[0], "$growthYears"] },
              { $in: [profitGrowthYears[1], "$growthYears"] },
              { $in: [profitGrowthYears[2], "$growthYears"] },
              { $in: [profitGrowthYears[3], "$growthYears"] },
              { $in: [profitGrowthYears[4], "$growthYears"] }
            ]
          }
        }
      },
      {
        $project: {
          stockCode: "$_id",
          _id: 0
        }
      }
    ]);
    
    if (profitGrowthStocks.length === 0) {
      return [];
    }
    
    const profitGrowthStockCodes = profitGrowthStocks.map(stock => stock.stockCode);
    
    // 第三步：查询满足当前市盈率和股息率条件的股票
    const finalStocks = await DailyStockData.find({
      stockCode: { $in: profitGrowthStockCodes },
      peRatioTTM: { $lt: 20, $ne: null },
      dividendYieldTTM: { $gt: 3, $ne: null }
    }, {
      stockCode: 1,
      closePrice: 1,
      peRatioTTM: 1,
      dividendYieldTTM: 1,
      _id: 0
    });
    
    return finalStocks;
  } catch (error) {
    throw new Error(`查询失败: ${error.message}`);
  }
};
