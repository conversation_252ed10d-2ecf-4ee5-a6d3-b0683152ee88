/* 智能筛选页面样式 */
@import '../../../styles/base.wxss';

.ai-filter-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding-bottom: var(--spacing-xxl);
}

/* ==================== 页面标题 ==================== */
.page-header {
  background: var(--gradient-primary);
  padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-xxl);
  color: var(--text-inverse);
}

.title-section {
  text-align: center;
}

.page-title {
  font-size: var(--font-size-title);
  font-weight: var(--font-weight-bold);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-base);
  opacity: 0.9;
  line-height: 1.6;
  display: block;
}

/* ==================== 智能输入区域 ==================== */
.ai-input-section {
  margin: calc(-1 * var(--spacing-lg)) var(--spacing-lg) var(--spacing-xl);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.input-container {
  padding: var(--spacing-xl);
}

.input-header {
  margin-bottom: var(--spacing-lg);
}

.input-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.input-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  display: block;
}

.input-area {
  position: relative;
}

.ai-input {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-lg);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  line-height: 1.6;
  transition: var(--transition-base);
  box-sizing: border-box;
}

.ai-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(27, 79, 114, 0.1);
}

.input-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-sm);
}

.char-count {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* ==================== 示例区域 ==================== */
.examples-section {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
  border-top: 1rpx solid var(--divider);
  background-color: var(--bg-secondary);
}

.examples-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  display: block;
  margin-bottom: var(--spacing-base);
  margin-top: var(--spacing-lg);
}

.examples-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.example-item {
  padding: var(--spacing-base) var(--spacing-lg);
  background-color: var(--bg-primary);
  border-radius: var(--radius-base);
  border: 1rpx solid var(--border-light);
  transition: var(--transition-fast);
  cursor: pointer;
}

.example-item:active {
  background-color: var(--bg-tertiary);
  transform: scale(0.98);
}

.example-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* ==================== 生成按钮 ==================== */
.generate-section {
  padding: 0 var(--spacing-lg) var(--spacing-xl);
}

.ai-filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-base);
}

.btn-parse {
  background: #1B4F72;
  color: #FFFFFF;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-parse.active {
  background: #1B4F72;
  color: #FFFFFF;
}

.btn-parse.active:active {
  opacity: 0.8;
}

.btn-parse.loading {
  background: #E0E0E0;
  color: #999999;
  opacity: 0.6;
  cursor: not-allowed;
}

/* ==================== 结果展示区域 ==================== */
.result-section {
  padding: 0 var(--spacing-lg);
}

.result-header {
  margin-bottom: var(--spacing-lg);
}

.result-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  display: block;
  text-align: center;
}

/* 卡片通用样式 */
.analysis-card,
.scripts-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-base);
  background: linear-gradient(135deg, var(--primary-lighter) 0%, var(--primary-light) 100%);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-inverse);
}

.card-content {
  padding: var(--spacing-xl);
}

.analysis-text {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: 1.7;
  white-space: pre-wrap;
}

/* 脚本列表 */
.scripts-list {
  padding: var(--spacing-base) 0;
}

.script-item {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1rpx solid var(--divider);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.script-item:last-child {
  border-bottom: none;
}

.script-header {
  flex: 1;
  margin-right: var(--spacing-lg);
}

.script-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-xs);
  font-family: 'SF Mono', Monaco, monospace;
}

.script-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  display: block;
}

.script-actions {
  flex-shrink: 0;
}

.btn-execute {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-success);
  color: var(--text-inverse);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.btn-execute:active {
  background-color: #229954;
  transform: scale(0.95);
}

/* 重新生成按钮 */
.regenerate-section {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.btn-regenerate {
  padding: var(--spacing-base) var(--spacing-xl);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  border: 1rpx solid var(--border-medium);
  transition: var(--transition-fast);
}

.btn-regenerate:active {
  background-color: var(--bg-disabled);
  transform: scale(0.98);
}

/* ==================== 错误状态 ==================== */
.error-section {
  padding: 0 var(--spacing-lg);
}

.error-card {
  background-color: var(--color-danger-light);
  border: 1rpx solid var(--color-danger);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.error-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: var(--spacing-base);
}

.error-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-danger);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.error-message {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: 1.6;
  display: block;
  margin-bottom: var(--spacing-lg);
}

.btn-retry {
  padding: var(--spacing-base) var(--spacing-xl);
  background-color: var(--color-danger);
  color: var(--text-inverse);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.btn-retry:active {
  background-color: #C0392B;
  transform: scale(0.95);
}



/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 375px) {
  .ai-input-section {
    margin-left: var(--spacing-base);
    margin-right: var(--spacing-base);
  }

  .input-container {
    padding: var(--spacing-lg);
  }

  .generate-section {
    padding: 0 var(--spacing-base) var(--spacing-xl);
  }

  .result-section,
  .error-section {
    padding: 0 var(--spacing-base);
  }
}

/* ==================== 动画效果 ==================== */
.result-section {
  animation: fadeInUp 0.5s ease-out;
}

.error-section {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 深色模式支持 ==================== */
/* 注意：微信小程序对媒体查询支持有限，此部分可能需要通过JS动态控制 */
/*
@media (prefers-color-scheme: dark) {
  .ai-input {
    background-color: #2D2D2D;
    border-color: #404040;
    color: #FFFFFF;
  }

  .example-item {
    background-color: #2D2D2D;
    border-color: #404040;
  }

  .example-text {
    color: #B0B0B0;
  }
}
*/
