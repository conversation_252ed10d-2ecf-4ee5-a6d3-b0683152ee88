const { success } = require('../../utils/response');
const { executeScriptByFileName, requireAndExecuteScript } = require('../../services/filter/executeScriptService');

/**
 * 按文件名在沙箱中执行已生成的筛选脚本
 * @route POST /api/v1/filter/execute-script-sandbox
 * @body {string} fileName 脚本文件名（可不带.js）
 */
const executeScriptSandboxController = async (req, res, next) => {
  try {
    const { fileName } = req.body;
    const execResult = await executeScriptByFileName(fileName);
    return success(res, 200, '脚本沙箱执行成功', execResult);
  } catch (err) {
    console.error('[脚本沙箱执行] 失败:', err);
    next(err);
  }
};

/**
 * 直接 require 并执行脚本（需人工审核，风险更高）
 * @route POST /api/v1/filter/execute-script-require
 * @body {string} fileName 脚本文件名（可不带.js）
 */
const executeScriptRequireController = async (req, res, next) => {
  try {
    const { fileName } = req.body;
    const execResult = await requireAndExecuteScript(fileName);
    return success(res, 200, '脚本require执行成功', execResult);
  } catch (err) {
    console.error('[脚本require执行] 失败:', err);
    next(err);
  }
};

module.exports = {
  executeScriptSandboxController,
  executeScriptRequireController
};
