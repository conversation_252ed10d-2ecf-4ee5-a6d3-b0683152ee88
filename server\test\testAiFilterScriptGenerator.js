const { generateFilterScript } = require('../src/services/filter/aiFilterScriptGeneratorService');
const fs = require('fs');
const path = require('path');

/**
 * 保存结果到文件
 * @param {Object} result - 测试结果
 * @param {string} testCaseName - 测试用例名称
 * @param {string} description - 测试描述
 */
const saveResultToFile = (result, testCaseName, description) => {
  try {
    // 创建结果目录
    const resultsDir = path.join(__dirname, 'test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // 生成文件名（使用时间戳确保唯一性）
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `${testCaseName}_${timestamp}.json`;
    const filePath = path.join(resultsDir, fileName);

    // 准备保存的数据
    const dataToSave = {
      testCase: testCaseName,
      description: description,
      timestamp: new Date().toISOString(),
      result: result
    };

    // 保存到文件
    fs.writeFileSync(filePath, JSON.stringify(dataToSave, null, 2), 'utf8');
    console.log(`✅ 结果已保存到: ${filePath}`);
    
    return filePath;
  } catch (error) {
    console.error(`❌ 保存文件失败:`, error.message);
    return null;
  }
};

/**
 * 测试AI智能生成筛选脚本功能
 */
const testGenerateFilterScript = async () => {
  try {
    console.log('开始测试AI智能生成筛选脚本功能...\n');

    // 测试用例1：简单的连续年度筛选（应该不拆分）
    const testCase1 = "找出连续三年净资产收益率大于15%,连续5年净利润增长率大于5%,且当前市盈率小于20,股息率大于3%的股票";
    console.log('测试用例1（简单逻辑）:', testCase1);
    const result1 = await generateFilterScript(testCase1);
    console.log('结果1:', JSON.stringify(result1, null, 2));
    
    // 保存结果1到文件
    saveResultToFile(result1, 'testCase1', testCase1);
    
    // console.log('\n' + '='.repeat(80) + '\n');

    // // 测试用例2：简单的市盈率筛选（应该不拆分）
    // const testCase2 = "找出当天最高涨幅大于8%的股票";
    // console.log('测试用例2（简单逻辑）:', testCase2);
    // const result2 = await generateFilterScript(testCase2);
    // console.log('结果2:', JSON.stringify(result2, null, 2));
    
    // // 保存结果2到文件
    // saveResultToFile(result2, 'testCase2', testCase2);
    
    // console.log('\n' + '='.repeat(80) + '\n');

    // // 测试用例3：复杂的计算逻辑（应该拆分）
    // const testCase3 = "找出近三年净利润增长率平均超过20%，且ROE呈上升趋势的股票";
    // console.log('测试用例3（复杂逻辑）:', testCase3);
    // const result3 = await generateFilterScript(testCase3);
    // console.log('结果3:', JSON.stringify(result3, null, 2));
    
    // // // 保存结果3到文件
    // saveResultToFile(result3, 'testCase3', testCase3);

    console.log('\n测试完成！');
    console.log(`📁 所有结果文件保存在: ${path.join(__dirname, 'test-results')} 目录中`);

  } catch (error) {
    console.error('测试失败:', error.message);
  }
};

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testGenerateFilterScript();
}

module.exports = {
  testGenerateFilterScript,
  saveResultToFile
};
