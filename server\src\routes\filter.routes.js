const express = require('express');
const router = express.Router();
const baseStockFilterController = require('../controllers/filter/baseStockFilterController');
const getSupportedConditionsController = require('../controllers/filter/getSupportedConditionsController');
const saveFilterConditionController = require('../controllers/filter/saveFilterConditionController');
const parseFilterConditionController = require('../controllers/filter/parseFilterConditionController');
const { parseAudioFilterConditionController } = require('../controllers/filter/parseAudioFilterConditionController');
const { validateFilterRequest } = require('../validators/filter/baseStockFilterValidator');
const { 
  validateSaveConditionRequest, 
  validateDeleteConditionRequest,
  validateRenameConditionRequest
} = require('../validators/filter/saveFilterConditionValidator');
const { validateParseFilterConditionRequest } = require('../validators/filter/parseFilterConditionValidator');
const { validateParseAudioFilterCondition } = require('../validators/filter/parseAudioFilterConditionValidator');
const { validateAiGenerateScriptRequest } = require('../validators/filter/aiFilterScriptGeneratorValidator');
const { aiGenerateFilterScriptController } = require('../controllers/filter/aiFilterScriptGeneratorController');
const { executeScriptSandboxController, executeScriptRequireController } = require('../controllers/filter/executeScriptController');
const { validateExecuteScriptRequest } = require('../validators/filter/executeScriptValidator');
const { protect } = require('../middlewares/auth');

/**
 * @route POST /api/filter/stocks
 * @description 根据条件筛选股票
 * @access Public
 * @body {Object} filterConditions - 筛选条件
 * @body {Object} [filterConditions.dailyConditions] - 当日数据筛选条件
 * @body {Object} [filterConditions.financialConditions] - 财务数据筛选条件
 * @body {Object} [filterConditions.basicInfoConditions] - 基本信息筛选条件
 * @body {string} [targetDate] - 目标日期 (YYYY-MM-DD)
 * @returns {Object} 筛选结果 - 包含符合条件的证券代码列表
 */
router.post('/base-filter-stocks', validateFilterRequest, baseStockFilterController.baseFilterStocks);

/**
 * @route GET /api/filter/supported-conditions
 * @description 获取支持的筛选条件列表
 * @access Public
 * @returns {Object} 支持的筛选条件列表
 */
router.get('/supported-conditions', getSupportedConditionsController.getSupportedConditions);

/**
 * @route POST /api/filter/parse-condition
 * @description 解析用户自然语言筛选条件
 * @access Private
 * @body {string} userInput - 用户输入的自然语言描述
 * @returns {Object} 解析结果 - 包含解析后的筛选条件
 */
router.post('/parse-condition', validateParseFilterConditionRequest, parseFilterConditionController.parseFilterConditionController);

/**
 * @route POST /api/filter/parse-audio-condition
 * @description 解析用户语音输入的筛选条件
 * @access Public
 * @body {string} audioData - Base64编码的音频数据
 * @body {string} format - 音频格式 (默认为wav) (支持的格式: wav, mp3, pcm, webm, ogg)
 * @returns {Object} 解析结果 - 包含语音识别文本和解析后的筛选条件
 */
router.post('/parse-audio-condition', validateParseAudioFilterCondition, parseAudioFilterConditionController);

/**
 * @route POST /api/filter/save-condition
 * @description 保存筛选条件
 * @access Private
 * @body {string} name - 筛选条件名称
 * @body {Object} filterConditions - 筛选条件
 * @returns {Object} 保存结果 - 包含筛选条件ID等信息
 */
router.post('/save-condition', protect, validateSaveConditionRequest, saveFilterConditionController.saveCondition);

/**
 * @route GET /api/filter/conditions
 * @description 获取用户所有保存的筛选条件列表（包含详情）
 * @access Private
 * @returns {Object} 筛选条件列表
 */
router.get('/conditions', protect, saveFilterConditionController.getConditionsList);

/**
 * @route DELETE /api/filter/conditions/:id
 * @description 删除筛选条件
 * @access Private
 * @param {string} id - 筛选条件ID
 * @returns {Object} 删除结果
 */
router.delete('/conditions/:id', protect, validateDeleteConditionRequest, saveFilterConditionController.deleteCondition);

/**
 * @route PATCH /api/filter/conditions/:id
 * @description 重命名筛选条件
 * @access Private
 * @param {string} id - 筛选条件ID
 * @body {string} name - 新的筛选条件名称
 * @returns {Object} 重命名结果
 */
router.patch('/conditions/:id', protect, validateRenameConditionRequest, saveFilterConditionController.renameCondition);

/**
 * @route POST /api/filter/ai-generate-script
 * @description 基于自然语言生成筛选逻辑脚本（解析 + 代码生成，并持久化到服务器指定目录）
 * @access Private （如需开放，可去掉 protect 中间件）
 * @body {string} userInput - 用户输入的自然语言需求描述
 * @returns {Object} 包含逻辑分析与生成的脚本列表
 */
router.post('/ai-generate-script', protect, validateAiGenerateScriptRequest, aiGenerateFilterScriptController);

/**
 * @route POST /api/filter/execute-script-sandbox
 * @description 按文件名在沙箱中执行已生成脚本
 * @access Private （建议仅内部或已登录用户）
 * @body {string} fileName 脚本文件名（可不带.js）
 */
router.post('/execute-script-sandbox', protect, validateExecuteScriptRequest, executeScriptSandboxController);

/**
 * @route POST /api/filter/execute-script-require
 * @description 直接require方式执行脚本（需人工审核后方可使用）
 * @access Private （高风险操作，务必鉴权）
 * @body {string} fileName 脚本文件名（可不带.js）
 */
router.post('/execute-script-require', protect, validateExecuteScriptRequest, executeScriptRequireController);

module.exports = router;
