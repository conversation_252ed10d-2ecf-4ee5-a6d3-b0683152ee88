const Joi = require('joi');

// 与服务端 sanitizeFileName 规则保持一致: 仅字母数字._- 且可不带 .js
const fileNameSchema = Joi.string()
  .trim()
  .pattern(/^[a-zA-Z0-9._-]+$/)
  .min(1)
  .max(100)
  .required()
  .messages({
    'string.empty': '文件名不能为空',
    'string.pattern.base': '文件名仅允许字母、数字、点、下划线、短横线',
    'string.max': '文件名长度不能超过100字符',
    'any.required': '必须提供文件名'
  });

const executeScriptRequestSchema = Joi.object({
  fileName: fileNameSchema
}).required();

const validateExecuteScriptRequest = (req, res, next) => {
  const { error, value } = executeScriptRequestSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(d => ({
      field: d.path.join('.'),
      message: d.message,
      value: d.context?.value
    }));
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedBody = value;
  next();
};

module.exports = { validateExecuteScriptRequest };
