const { generateFilterScript } = require('../../services/filter/aiFilterScriptGeneratorService');
const { success } = require('../../utils/response');

/**
 * AI生成筛选脚本控制器
 * @route POST /api/filter/ai-generate-script
 * @access Private (后续可接入鉴权中间件 protect)
 * @body {string} userInput - 用户自然语言描述
 * @returns {Object} 生成结果
 */
const aiGenerateFilterScriptController = async (req, res, next) => {
  try {
    const { userInput } = req.body;
    console.log('[AI脚本生成] 收到请求 userInput:', userInput);

    const result = await generateFilterScript(userInput);

    return success(res, 200, 'AI脚本生成成功', result);
  } catch (err) {
    console.error('[AI脚本生成] 失败:', err);
    next(err);
  }
};

module.exports = { aiGenerateFilterScriptController };