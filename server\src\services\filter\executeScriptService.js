const vm = require('vm');
const fs = require('fs');
const path = require('path');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');
const mongoose = require('mongoose');

// 生成脚本存放目录 (与脚本生成服务保持一致)
const GENERATED_FILTER_DIR = path.join(__dirname, '../../../scripts/generatedFilters');

// 确保目录存在（如果不存在，不抛异常，只在调用时判定）
const ensureGeneratedDir = () => {
  try {
    if (!fs.existsSync(GENERATED_FILTER_DIR)) return false;
    return true;
  } catch (_) { return false; }
};

// 文件名清洗与安全校验，防止路径遍历
const sanitizeFileName = (fileName) => {
  if (!fileName || typeof fileName !== 'string') throw new Error('文件名不能为空');
  // 去掉首尾空格
  let name = fileName.trim();
  // 不允许包含路径分隔符或上级目录引用
  if (name.includes('..') || name.includes('/') || name.includes('\\')) {
    throw new Error('非法文件名: 包含路径分隔符或..');
  }
  // 限制只允许字母数字、下划线、短横线、点
  if (!/^[a-zA-Z0-9._-]+$/.test(name)) {
    throw new Error('非法文件名: 仅支持字母/数字/._-');
  }
  if (!name.endsWith('.js')) name += '.js';
  return name;
};

/**
 * 根据已保存脚本文件名读取脚本内容并在沙箱中执行（与直接传字符串方式一致）
 * @param {string} fileName 生成目录中的脚本文件名（可不带.js）
 * @returns {Promise<Object>} 执行结果
 */
const executeScriptByFileName = async (fileName) => {
  try {
    console.log('[执行脚本][沙箱文件模式] fileName =', fileName);
    const ok = ensureGeneratedDir();
    if (!ok) throw new Error('生成脚本目录不存在，请先生成脚本');

    const safeName = sanitizeFileName(fileName);
    const fullPath = path.join(GENERATED_FILTER_DIR, safeName);
    if (!fs.existsSync(fullPath)) throw new Error('脚本文件不存在: ' + safeName);

    const code = await fs.promises.readFile(fullPath, 'utf8');
    // 只提取实际代码（忽略头部注释不需要特殊处理）
    // 进行安全校验
    validateScriptSafety(code);

    // 确保数据库连接
    await ensureDatabaseConnection();

    const result = await executeScriptInSandbox(code);
    return {
      success: true,
      result,
      executedAt: new Date().toISOString(),
      resultCount: Array.isArray(result) ? result.length : 1,
      fileName: safeName,
      executionMode: 'sandbox_file'
    };
  } catch (err) {
    console.error('[执行脚本][沙箱文件模式] 失败:', err);
    throw new Error('按文件名沙箱执行失败: ' + err.message);
  }
};

/**
 * 通过 require 方式加载脚本并执行导出的 executeLogic（非VM沙箱）。
 * 风险: 脚本在 Node 主进程上下文执行，应仅对经过人工审核的脚本使用。
 * @param {string} fileName 脚本文件名
 * @returns {Promise<Object>} 执行结果
 */
const requireAndExecuteScript = async (fileName) => {
  try {
    console.log('[执行脚本][require模式] fileName =', fileName);
    const ok = ensureGeneratedDir();
    if (!ok) throw new Error('生成脚本目录不存在，请先生成脚本');

    const safeName = sanitizeFileName(fileName);
    const fullPath = path.join(GENERATED_FILTER_DIR, safeName);
    if (!fs.existsSync(fullPath)) throw new Error('脚本文件不存在: ' + safeName);

    // 先读取内容做安全校验（虽然是require执行，但仍沿用既有静态检查）
    const code = await fs.promises.readFile(fullPath, 'utf8');
    validateScriptSafety(code);

    // 清理缓存，确保每次获取最新版本
    delete require.cache[require.resolve(fullPath)];

    // 确保数据库连接
    await ensureDatabaseConnection();

    const mod = require(fullPath);
    if (!mod || typeof mod.executeLogic !== 'function') {
      throw new Error('模块未导出 executeLogic 函数');
    }

    // 执行逻辑（如果需要参数，可在此扩展）
    const result = await mod.executeLogic();
    return {
      success: true,
      result,
      executedAt: new Date().toISOString(),
      resultCount: Array.isArray(result) ? result.length : 1,
      fileName: safeName,
      executionMode: 'require'
    };
  } catch (err) {
    console.error('[执行脚本][require模式] 失败:', err);
    throw new Error('require方式执行失败: ' + err.message);
  }
};

/**
 * 确保数据库连接
 */
const ensureDatabaseConnection = async () => {
  if (mongoose.connection.readyState === 0) {
    // 如果没有连接，尝试使用现有的数据库配置
    const connectDB = require('../../config/database');
    await connectDB();
  } else if (mongoose.connection.readyState === 1) {
    console.log('数据库连接正常');
  } else if (mongoose.connection.readyState === 2) {
    // 正在连接中，等待连接完成
    console.log('数据库正在连接中，等待连接完成...');
    await new Promise((resolve, reject) => {
      mongoose.connection.once('connected', resolve);
      mongoose.connection.once('error', reject);
      setTimeout(() => reject(new Error('数据库连接超时')), 10000);
    });
  } else {
    throw new Error('数据库连接状态异常');
  }
};

/**
 * 验证脚本安全性
 * @param {string} scriptCode 脚本代码
 */
const validateScriptSafety = (scriptCode) => {
  // 检查危险操作
  const dangerousPatterns = [
    /require\s*\(/,           // 禁止require其他模块
    /import\s+/,              // 禁止import
    /process\./,              // 禁止访问process对象
    /global\./,               // 禁止访问global对象
    /eval\s*\(/,              // 禁止eval
    /Function\s*\(/,          // 禁止Function构造器
    /fs\./,                   // 禁止文件系统操作
    /child_process/,          // 禁止子进程
    /exec\s*\(/,              // 禁止执行系统命令
    /spawn\s*\(/,             // 禁止spawn
    /\.\.\/\.\.\//,           // 禁止路径遍历
    /delete\s+/,              // 禁止delete操作
    /while\s*\(\s*true\s*\)/, // 禁止无限循环
    /for\s*\(\s*;\s*;\s*\)/   // 禁止无限for循环
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(scriptCode)) {
      throw new Error(`脚本包含不安全的操作: ${pattern.source}`);
    }
  }

  // 检查是否包含必要的函数结构
  if (!scriptCode.includes('executeLogic') && !scriptCode.includes('async')) {
    throw new Error('脚本必须包含executeLogic函数或async函数');
  }

  // 检查代码长度限制
  if (scriptCode.length > 10000) {
    throw new Error('脚本代码过长，超过安全限制');
  }
};

/**
 * 在沙箱环境中执行脚本
 * @param {string} scriptCode 脚本代码
 * @returns {Promise<any>} 执行结果
 */
const executeScriptInSandbox = async (scriptCode) => {
  try {
    // 创建安全的执行上下文
    const sandbox = {
      // 提供数据模型
      DailyStockData: DailyStockData,
      FinancialData: FinancialData,
      StockBasicInfo: StockBasicInfo,

      // 提供必要的全局对象
      console: {
        log: console.log,
        error: console.error,
        warn: console.warn
      },

      // 提供Date对象
      Date: Date,

      // 提供Promise
      Promise: Promise,

      // 提供setTimeout（限制时间）
      setTimeout: (fn, delay) => {
        if (delay > 30000) delay = 30000; // 最大30秒
        return setTimeout(fn, delay);
      },

      // 提供Error构造器
      Error: Error
    };

    // 创建VM上下文
    const context = vm.createContext(sandbox);

    // 包装代码，使用Promise来处理异步执行
    const wrappedCode = `
      (async () => {
        ${scriptCode}

        // 尝试执行函数
        if (typeof executeLogic === 'function') {
          return await executeLogic();
        } else {
          throw new Error('未找到executeLogic函数');
        }
      })();
    `;

    // 设置执行超时时间（30秒）
    const timeout = 30000;

    // 在VM中执行代码并等待结果
    const resultPromise = vm.runInContext(wrappedCode, context, {
      timeout: timeout,
      displayErrors: true
    });

    // 等待Promise完成
    const result = await Promise.race([
      resultPromise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('脚本执行超时')), timeout)
      )
    ]);

    return result;

  } catch (error) {
    if (error.message.includes('Script execution timed out') || error.message.includes('脚本执行超时')) {
      throw new Error('脚本执行超时，请检查是否存在无限循环或长时间运行的操作');
    }
    throw new Error(`脚本执行错误: ${error.message}`);
  }
};

module.exports = {
  executeScriptByFileName,
  requireAndExecuteScript
};
