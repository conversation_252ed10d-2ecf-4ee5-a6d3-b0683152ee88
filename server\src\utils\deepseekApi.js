const axios = require('axios');

// 从环境变量读取配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// DeepSeek API 默认配置
const DEFAULT_AI_CONFIG = {
  temperature: 0.7,
  maxTokens: 4000,
  timeout: 60000
};

/**
 * 调用DeepSeek API生成文本
 * @param {String} userPrompt 用户输入提示
 * @param {String} systemPrompt 系统提示（可选）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} 生成的文本
 */
const generateTextWithDeepSeek = async (userPrompt, systemPrompt = null, config = {}) => {
  try {
    // 合并配置
    const aiConfig = { ...DEFAULT_AI_CONFIG, ...config };
    
    // 确保endpoint没有结尾的斜杠
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`调用DeepSeek API: ${url}`);
    
    // 构建消息数组
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('DeepSeek API调用失败:', error);
    let errorMessage = 'DeepSeek AI总结生成失败，请稍后重试';
    
    if (error.response) {
      errorMessage += `(状态码: ${error.response.status}, 错误信息: ${JSON.stringify(error.response.data)})`;
    } else if (error.request) {
      errorMessage += `(请求错误: ${error.message})`;
    } else {
      errorMessage += `(错误: ${error.message})`;
    }
    
    throw new Error(errorMessage);
  }
};

module.exports = { generateTextWithDeepSeek };