const dailyStockDataModelInfo = `
数据表：DailyStockData (MongoDB集合: daily_stock_data)

完整字段列表：
{
  // 基本信息
  stockCode: String,        // 证券代码
  timestamp: Date,          // 时间戳
  
  // 价格信息
  closePrice: Number,       // 收盘价
  priceChange: Number,      // 涨跌额
  changePercent: Number,    // 涨跌幅(%)
  previousClose: Number,    // 昨收
  openPrice: Number,        // 今开
  highPrice: Number,        // 最高
  lowPrice: Number,         // 最低
  
  // 交易信息
  volume: Number,           // 成交量
  turnover: Number,         // 成交额
  turnoverRate: Number,     // 换手率(%)
  amplitude: Number,        // 振幅(%)
  limitUpPrice: Number,     // 涨停价
  limitDownPrice: Number,   // 跌停价
  averagePrice: Number,     // 均价
  
  // 52周信息
  week52High: Number,       // 52周最高
  week52Low: Number,        // 52周最低
  ytdGain: Number,          // 今年以来涨幅(%)
  
  // 估值指标
  peRatioDynamic: Number,   // 市盈率(动)
  peRatioStatic: Number,    // 市盈率(静)
  peRatioTTM: Number,       // 市盈率(TTM)
  pbRatio: Number,          // 市净率
  
  // 每股指标
  earningsPerShare: Number,     // 每股收益
  bookValuePerShare: Number,    // 每股净资产
  dividendTTM: Number,          // 股息(TTM)
  dividendYieldTTM: Number,     // 股息率(TTM)(%)
  
  // 市值信息
  totalMarketCap: Number,       // 总市值
  circulatingMarketCap: Number, // 流通市值
  
  // 技术指标
  volumeRatio: Number,          // 量比
  priceVelocity: Number,        // 涨速(%)
  fiveMinuteChange: Number,     // 5分钟涨跌(%)
  sixtyDayChange: Number,       // 60日涨跌幅(%)
}

使用示例：

// 按条件筛选
const highVolumeStocks = await DailyStockData.find({
  volume: { $gt: 1000000 },
  changePercent: { $gt: 5 }
});

重要提示：
- 所有Number类型字段默认值为null，查询时需要注意过滤
- 日期格式：'YYYY-MM-DD' 或 Date对象
`;

const financialDataModelInfo = `
// MongoDB财务数据表结构 - FinancialData集合
{
  // === 基本信息 ===
  stockCode: String,        // 证券代码，如 "000001"
  reportType: String,       // 报表类型: "按年度" | "按报告期" | "按单季度"
  fiscalYear: Number,       // 财政年度，如 2024
  reportDate: Date,         // 报告日期
  
  // === 财务数据 ===
  data: {
    keyMetrics: {
      // 盈利能力指标
      netProfit: Number,                    // 净利润(亿元)
      netProfitGrowthRate: Number,          // 净利润同比增长率(%)
      nonRecurringNetProfit: Number,        // 扣非净利润(亿元)
      nonRecurringNetProfitGrowthRate: Number, // 扣非净利润同比增长率(%)
      totalRevenue: Number,                 // 营业总收入(亿元)
      totalRevenueGrowthRate: Number,       // 营业总收入同比增长率(%)
      netProfitMargin: Number,              // 销售净利率(%)
      grossProfitMargin: Number,            // 销售毛利率(%)
      
      // 每股指标
      basicEarningsPerShare: Number,        // 基本每股收益
      bookValuePerShare: Number,            // 每股净资产
      operatingCashFlowPerShare: Number,    // 每股经营现金流
      
      // 盈利质量指标
      returnOnEquity: Number,               // 净资产收益率(%)
      
      // 偿债能力指标
      currentRatio: Number,                 // 流动比率
      quickRatio: Number,                   // 速动比率
      debtToAssetRatio: Number,            // 资产负债率(%)
      
      // 营运能力指标
      inventoryTurnoverRatio: Number,       // 存货周转率
      inventoryTurnoverDays: Number,        // 存货周转天数
      accountsReceivableTurnoverDays: Number // 应收账款周转天数
    }
  }
}

// === 重要说明 ===
1. 集合名称: 'financial_data'
2. 查询年度使用字段: fiscalYear (数字类型，如2024)
3. 财务指标路径: data.keyMetrics.字段名
4. reportType默认使用 "按年度"

// === 查询示例 ===
// 示例1: 查询2024年净利润增长率
db.financial_data.find({
  fiscalYear: 2024,
  reportType: "按年度"
}, {
  stockCode: 1,
  "data.keyMetrics.netProfitGrowthRate": 1
})

// 示例2: 查询指定股票近3年净利润
db.financial_data.find({
  stockCode: "000001",
  fiscalYear: { $gte: 2022, $lte: 2024 },
  reportType: "按年度"
}, {
  fiscalYear: 1,
  "data.keyMetrics.netProfit": 1
}).sort({ fiscalYear: 1 })

// 示例3: 查询2024年ROE大于15%的股票
db.financial_data.find({
  fiscalYear: 2024,
  reportType: "按年度",
  "data.keyMetrics.returnOnEquity": { $gt: 15 }
}, {
  stockCode: 1,
  "data.keyMetrics.returnOnEquity": 1
})
`;

const stockBasicInfoModelInfo = `
// MongoDB股票基本信息表结构 - StockBasicInfo集合
{
  // === 基本信息 ===
  stockCode: String,        // 证券代码，如 "000001"
  stockName: String,        // 证券简称，如 "平安银行"
  board: String,           // 板块，如 "主板"、"创业板"、"科创板"
  industry: String,        // 行业，如 "银行"、"软件服务"
  listingDate: Date,       // 上市时间
}

// === 重要说明 ===
1. 集合名称: 'stock_basic_info'
2. stockCode 为唯一索引
3. 支持按板块、行业、上市时间查询

// === 查询示例 ===
// 示例1: 根据股票代码查询基本信息
db.stock_basic_info.findOne({
  stockCode: "000001"
})

// 示例2: 查询银行行业的所有股票
db.stock_basic_info.find({
  industry: "银行"
}, {
  stockCode: 1,
  stockName: 1,
  board: 1
})

// 示例3: 查询主板上市的股票
db.stock_basic_info.find({
  board: "主板"
}, {
  stockCode: 1,
  stockName: 1,
  industry: 1
})

// 示例5: 查询2020年后上市的股票
db.stock_basic_info.find({
  listingDate: { $gte: new Date("2020-01-01") }
}, {
  stockCode: 1,
  stockName: 1,
  listingDate: 1
}).sort({ listingDate: -1 })
`;

module.exports = {
  dailyStockDataModelInfo,
  financialDataModelInfo,
  stockBasicInfoModelInfo
};

