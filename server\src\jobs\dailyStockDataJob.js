const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const iconv = require('iconv-lite'); // 需要安装这个包

class DailyStockDataJob {
  constructor() {
    this.isRunning = false;
    this.lastRunTime = null;
    this.logPath = path.join(__dirname, '../../logs/daily-job.log');
  }

  // 记录日志
  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    
    console.log(message);
    
    // 确保logs目录存在
    const logsDir = path.dirname(this.logPath);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    // 写入日志文件
    fs.appendFileSync(this.logPath, logMessage, 'utf8');
  }

  // 检测可用的Python命令
  async detectPythonCommand() {
    // 允许通过环境变量显式指定 Python 解释器
    if (process.env.PYTHON_BIN) {
      this.log(`使用环境变量 PYTHON_BIN 指定的 Python: ${process.env.PYTHON_BIN}`);
      return process.env.PYTHON_BIN;
    }

    const possibleCommands = ['python', 'python3', 'py'];
    
    for (const cmd of possibleCommands) {
      try {
        await new Promise((resolve, reject) => {
          const testProcess = spawn(cmd, ['--version'], { stdio: 'pipe' });
          testProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Command ${cmd} failed with code ${code}`));
            }
          });
          testProcess.on('error', reject);
        });
        
        this.log(`检测到可用的Python命令: ${cmd}`);
        return cmd;
      } catch (error) {
        continue;
      }
    }
    
    throw new Error('未找到可用的Python命令。请确保Python已正确安装并在PATH中。');
  }

  // 执行Python脚本
  async runPythonScript() {
    return new Promise(async (resolve, reject) => {
      this.log('开始执行Python数据获取脚本...');
      
      try {
        // 检测Python命令
        const pythonCmd = await this.detectPythonCommand();
        
        const pythonScriptPath = path.join(__dirname, '../../data/scripts/get_daily_stock_data.py');
        
        // 检查Python脚本是否存在
        if (!fs.existsSync(pythonScriptPath)) {
          throw new Error(`Python脚本不存在: ${pythonScriptPath}`);
        }
        
        this.log(`使用Python命令: ${pythonCmd}`);
        this.log(`执行脚本: ${pythonScriptPath}`);

        // 先执行 akshare 升级安装（使用清华镜像）
        await this.installOrUpgradeAkshare(pythonCmd);
        
        // 设置环境变量，确保Python输出UTF-8编码
        const env = { 
          ...process.env, 
          PYTHONIOENCODING: 'utf-8',
          PYTHONUNBUFFERED: '1'
        };
        
        const pythonProcess = spawn(pythonCmd, ['-u', pythonScriptPath], {
          cwd: path.dirname(pythonScriptPath),
          stdio: ['pipe', 'pipe', 'pipe'],
          env: env
        });

        let stdout = '';
        let stderr = '';

        pythonProcess.stdout.on('data', (data) => {
          // 处理编码问题
          let output;
          try {
            output = iconv.decode(data, 'utf8');
          } catch (e) {
            try {
              output = iconv.decode(data, 'gbk');
            } catch (e2) {
              output = data.toString();
            }
          }
          
          stdout += output;
          // 过滤掉空行和只包含换行符的行
          const lines = output.trim().split('\n').filter(line => line.trim());
          lines.forEach(line => {
            this.log(`Python输出: ${line}`);
          });
        });

        pythonProcess.stderr.on('data', (data) => {
          // 处理编码问题
          let error;
          try {
            error = iconv.decode(data, 'utf8');
          } catch (e) {
            try {
              error = iconv.decode(data, 'gbk');
            } catch (e2) {
              error = data.toString();
            }
          }
          
          stderr += error;
          
          // 过滤进度条和其他非关键错误信息
          const errorLines = error.trim().split('\n').filter(line => {
            const trimmedLine = line.trim();
            // 过滤进度条相关的输出
            if (trimmedLine.includes('%|') || 
                trimmedLine.includes('it/s') || 
                trimmedLine.match(/^\d+%/) ||
                trimmedLine.includes('█') ||
                trimmedLine === '') {
              return false;
            }
            return true;
          });
          
          // 只记录真正的错误信息
          errorLines.forEach(line => {
            if (line.trim()) {
              this.log(`Python警告: ${line}`);
            }
          });
        });

        pythonProcess.on('close', (code) => {
          if (code === 0) {
            this.log('Python脚本执行成功');
            resolve({ success: true, stdout, stderr });
          } else {
            this.log(`Python脚本执行失败，退出码: ${code}`);
            // 只有在真正有错误信息时才显示
            const realErrors = stderr.split('\n').filter(line => {
              const trimmedLine = line.trim();
              return trimmedLine && 
                     !trimmedLine.includes('%|') && 
                     !trimmedLine.includes('it/s') && 
                     !trimmedLine.match(/^\d+%/) &&
                     !trimmedLine.includes('█');
            });
            
            if (realErrors.length > 0) {
              reject(new Error(`Python脚本执行失败，退出码: ${code}\n错误信息: ${realErrors.join('\n')}`));
            } else {
              // 如果没有真正的错误信息，可能是正常结束但返回了非0退出码
              this.log('Python脚本可能正常执行完成（忽略进度条相关的stderr输出）');
              resolve({ success: true, stdout, stderr });
            }
          }
        });

        pythonProcess.on('error', (error) => {
          this.log(`Python脚本执行出错: ${error.message}`);
          reject(error);
        });
        
      } catch (error) {
        this.log(`Python环境检测失败: ${error.message}`);
        reject(error);
      }
    });
  }

  // 安装或升级 akshare 库
  async installOrUpgradeAkshare(pythonCmd) {
    return new Promise((resolve, reject) => {
      this.log('开始执行 akshare 升级安装: pip install akshare --upgrade -i https://pypi.tuna.tsinghua.edu.cn/simple');

      // 兼容不同 Python (部分环境需要 -m pip)
      const args = ['-m', 'pip', 'install', 'akshare', '--upgrade', '-i', 'https://pypi.tuna.tsinghua.edu.cn/simple'];
      const installProcess = spawn(pythonCmd, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
      });

      let stdout = '';
      let stderr = '';

      installProcess.stdout.on('data', (data) => {
        let output;
        try {
          output = iconv.decode(data, 'utf8');
        } catch (e) {
          output = data.toString();
        }
        stdout += output;
        const lines = output.trim().split('\n').filter(l => l.trim());
        lines.forEach(l => this.log(`pip输出: ${l}`));
      });

      installProcess.stderr.on('data', (data) => {
        let error;
        try {
          error = iconv.decode(data, 'utf8');
        } catch (e) {
          error = data.toString();
        }
        stderr += error;
        const lines = error.trim().split('\n').filter(l => l.trim());
        // 过滤掉非关键的进度条噪声
        lines.forEach(l => {
          if (!/^(Collecting|Downloading|Using cached|Building wheel|Preparing metadata)/.test(l)) {
            this.log(`pip警告/错误: ${l}`);
          }
        });
      });

      installProcess.on('close', (code) => {
        if (code === 0) {
          this.log('akshare 升级安装完成');
          resolve();
        } else {
          this.log(`akshare 升级安装失败，退出码: ${code}`);
          reject(new Error(`akshare 升级安装失败，退出码: ${code}\n${stderr}`));
        }
      });

      installProcess.on('error', (err) => {
        this.log(`执行 pip 出错: ${err.message}`);
        reject(err);
      });
    });
  }

  // 执行Node.js导入脚本
  async runImportScript() {
    return new Promise((resolve, reject) => {
      this.log('开始执行数据库导入脚本...');
      
      const importScriptPath = path.join(__dirname, '../../scripts/importDailyStockData.js');
      
      // 检查导入脚本是否存在
      if (!fs.existsSync(importScriptPath)) {
        const error = new Error(`导入脚本不存在: ${importScriptPath}`);
        this.log(error.message);
        reject(error);
        return;
      }
      
      const nodeProcess = spawn('node', [importScriptPath], {
        cwd: path.dirname(importScriptPath),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, NODE_OPTIONS: '--max-old-space-size=4096' }
      });

      let stdout = '';
      let stderr = '';

      nodeProcess.stdout.on('data', (data) => {
        const output = data.toString('utf8');
        stdout += output;
        const lines = output.trim().split('\n').filter(line => line.trim());
        lines.forEach(line => {
          this.log(`Import输出: ${line}`);
        });
      });

      nodeProcess.stderr.on('data', (data) => {
        const error = data.toString('utf8');
        stderr += error;
        const lines = error.trim().split('\n').filter(line => line.trim());
        lines.forEach(line => {
          this.log(`Import错误: ${line}`);
        });
      });

      nodeProcess.on('close', (code) => {
        if (code === 0) {
          this.log('数据库导入脚本执行成功');
          resolve({ success: true, stdout, stderr });
        } else {
          this.log(`数据库导入脚本执行失败，退出码: ${code}`);
          reject(new Error(`数据库导入脚本执行失败，退出码: ${code}\n错误信息: ${stderr}`));
        }
      });

      nodeProcess.on('error', (error) => {
        this.log(`数据库导入脚本执行出错: ${error.message}`);
        reject(error);
      });
    });
  }

  // 检查CSV文件是否存在
  checkCSVFile() {
    const csvPath = path.join(__dirname, '../../data/csv/企业当日数据_全字段_雪球为主_当日数据.csv');
    return fs.existsSync(csvPath);
  }

  // 执行完整的任务流程
  async run() {
    if (this.isRunning) {
      this.log('任务正在运行中，跳过本次执行');
      return;
    }

    this.isRunning = true;
    this.lastRunTime = new Date();
    
    try {
      this.log('========== 开始执行每日股票数据任务 ==========');
      
      // 步骤1: 执行Python脚本获取数据
      await this.runPythonScript();
      
      // 步骤2: 检查CSV文件是否生成
      if (!this.checkCSVFile()) {
        throw new Error('Python脚本执行后未生成CSV文件');
      }
      this.log('CSV文件生成成功');
      
      // 步骤3: 执行数据库导入
      await this.runImportScript();
      
      this.log('========== 每日股票数据任务执行完成 ==========');
      
    } catch (error) {
      this.log(`任务执行失败: ${error.message}`);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  // 获取任务状态
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastRunTime: this.lastRunTime
    };
  }
}

module.exports = DailyStockDataJob;
