/**
 * API服务模块
 */
const { post, get, del, patch, put, upload } = require('../utils/request');

/**
 * 筛选股票相关API
 */
const filter = {
  /**
   * 基础股票筛选
   * @param {Object} filterConditions 筛选条件
   * @param {Object} [filterConditions.dailyConditions] 当日数据筛选条件
   * @param {Object} [filterConditions.financialConditions] 财务数据筛选条件  
   * @param {Object} [filterConditions.basicInfoConditions] 基本信息筛选条件
   * @param {string} [targetDate] 目标日期 (YYYY-MM-DD)
   * @returns {Promise<Object>} 筛选结果
   */
  baseFilterStocks(filterConditions, targetDate) {
    console.log("API调用 - 筛选条件:", filterConditions);
    console.log("API调用 - 目标日期:", targetDate);
    return post('/filter/base-filter-stocks', { filterConditions, targetDate });
  },

  /**
   * 保存筛选条件
   * @param {string} name 筛选条件名称
   * @param {Object} filterConditions 筛选条件（不包含targetDate）
   * @param {Object} [filterConditions.dailyConditions] 当日数据筛选条件
   * @param {Object} [filterConditions.financialConditions] 财务数据筛选条件
   * @param {Object} [filterConditions.basicInfoConditions] 基本信息筛选条件
   * @returns {Promise<Object>} 保存结果
   */
  saveFilterCondition(name, filterConditions) {
    return post('/filter/save-condition', { name, filterConditions }, true);
  },

  /**
   * 获取用户所有保存的筛选条件列表（包含详情）
   * @returns {Promise<Object>} 筛选条件列表
   */
  getUserFilterConditions() {
    return get('/filter/conditions', {}, true);
  },

  /**
   * 删除筛选条件
   * @param {string} conditionId 筛选条件ID
   * @returns {Promise<Object>} 删除结果
   */
  deleteFilterCondition(conditionId) {
    return del(`/filter/conditions/${conditionId}`, {}, true);
  },

  /**
   * 重命名筛选条件
   * @param {string} conditionId 筛选条件ID
   * @param {string} newName 新的筛选条件名称
   * @returns {Promise<Object>} 重命名结果
   */
  renameFilterCondition(conditionId, newName) {
    return patch(`/filter/conditions/${conditionId}`, { name: newName }, true);
  },

  /**
   * 解析自然语言筛选条件
   * @param {string} userInput 自然语言筛选条件
   * @returns {Promise<Object>} 解析结果
   */
  parseFilterCondition(userInput) {
    return post('/filter/parse-condition', { userInput });
  },

  /**
   * AI 生成筛选脚本
   * @param {string} userInput 自然语言描述
   * @returns {Promise<Object>} 生成结果（包含逻辑分析与脚本列表）
   */
  generateAiFilterScript(userInput) {
    return post('/filter/ai-generate-script', { userInput }, true); // 需要鉴权
  }
  ,

  /**
   * 按文件名沙箱执行生成的筛选脚本
   * @param {string} fileName 脚本文件名（可不带.js）
   * @returns {Promise<Object>} 执行结果
   */
  executeScriptSandbox(fileName) {
    return post('/filter/execute-script-sandbox', { fileName }, true);
  },

  /**
   * 按文件名 require 执行脚本（高风险，需鉴权）
   * @param {string} fileName 脚本文件名（可不带.js）
   * @returns {Promise<Object>} 执行结果
   */
  executeScriptRequire(fileName) {
    return post('/filter/execute-script-require', { fileName }, true);
  }
};

module.exports = filter;