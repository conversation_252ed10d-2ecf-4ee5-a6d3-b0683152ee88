const Joi = require('joi');

// 验证 schema
const aiFilterScriptGenerateSchema = Joi.object({
  userInput: Joi.string()
    .trim()
    .min(1)
    .max(800)
    .required()
    .messages({
      'string.empty': '用户输入不能为空',
      'string.min': '用户输入不能为空',
      'string.max': '用户输入不能超过800个字符',
      'any.required': '用户输入不能为空'
    })
}).required();

/**
 * 验证 AI 生成脚本请求
 */
const validateAiGenerateScriptRequest = (req, res, next) => {
  const { error, value } = aiFilterScriptGenerateSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: false
  });

  if (error) {
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors
    });
  }

  req.validatedBody = value;
  next();
};

module.exports = { validateAiGenerateScriptRequest };